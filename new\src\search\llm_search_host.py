import asyncio
import json
import logging
import os
import traceback
from typing import List, Literal, Optional

from .llm_search_mcp_client import MC<PERSON><PERSON>, create_mcp_client_from_config

try:
    from ..request.wrapper import RequestWrapper
except ImportError:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from request.wrapper import RequestWrapper

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_model_config():
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'model_config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"Model config loaded from: {config_path}")
        return config
    except Exception as e:
        logger.warning(f"Failed to load model config, using defaults: {e}")
        return {
            "search": {
                "host_llm": {
                    "model": "gemini-2.0-flash-thinking-exp-01-21",
                    "infer_type": "OpenAI"
                }
            }
        }

MODEL_CONFIG = load_model_config()

class LLM_search:

    def __init__(
        self,
        model: str = None,
        infer_type: str = None,
        max_workers: int = 10,
        use_memory: bool = True,
        max_context_messages: int = 10,
    ):
        # 从配置文件读取默认模型配置
        if model is None or infer_type is None:
            host_config = MODEL_CONFIG.get("search", {}).get("host_llm", {})
            model = model or host_config.get("model", "gemini-2.0-flash-thinking-exp-01-21")
            infer_type = infer_type or host_config.get("infer_type", "OpenAI")

        self.model = model
        self.infer_type = infer_type
        self.use_memory = use_memory
        self.request_wrapper = RequestWrapper(
            model=model,
            infer_type=infer_type,
            use_memory=use_memory,
            max_context_messages=max_context_messages
        )
        self._mcp_client: Optional[MCPClient] = None

        logger.info(f"LLM_search initialized with model: {model}, infer_type: {infer_type}, memory: {use_memory}")
    
    async def _get_mcp_client(self) -> MCPClient:
        if self._mcp_client is None or not self._mcp_client.is_connected:
            self._mcp_client = await create_mcp_client_from_config()
        return self._mcp_client
    
    async def _cleanup_client(self):
        if self._mcp_client and self._mcp_client.is_connected:
            await self._mcp_client.disconnect()
            self._mcp_client = None
    
    async def _get_available_tools(self) -> List[dict]:
        client = await self._get_mcp_client()
        return await client.list_tools()
    
    def _create_tool_selection_prompt(self, task_description: str, available_tools: List[dict]) -> str:
        tools_info = "\n".join([
            f"- {tool['name']}: {tool['description']}"
            for tool in available_tools
        ])
        
        return f"""你是一个搜索智能体。用户给你一个搜索任务，你需要选择最合适的工具来完成。

可用工具：
{tools_info}

用户任务：{task_description}

请分析任务需求，选择最合适的工具，并提供调用参数。注意：不要在参数中包含配置信息（如engine、model等），这些由Server自动处理。

请按以下JSON格式返回：
{{
    "selected_tool": "工具名称",
    "arguments": {{
        "参数名": "参数值"
    }},
    "reasoning": "选择理由"
}}"""

    async def _llm_select_and_call_tool(self, task_description: str, **user_args) -> dict:
        try:
            available_tools = await self._get_available_tools()
            
            prompt = self._create_tool_selection_prompt(task_description, available_tools)
            response = await self.request_wrapper.async_request(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1000
            )
            
            try:
                if "```json" in response:
                    json_start = response.find("```json") + 7
                    json_end = response.find("```", json_start)
                    json_text = response[json_start:json_end].strip()
                else:
                    json_text = response.strip()
                
                decision = json.loads(json_text)
                
                arguments = decision["arguments"]
                arguments.update(user_args)
                
                client = await self._get_mcp_client()
                result = await client.call_tool(decision["selected_tool"], arguments)
                
                logger.info(f"LLM selected tool: {decision['selected_tool']}, reasoning: {decision.get('reasoning', 'N/A')}")
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response: {e}")
                client = await self._get_mcp_client()
                return await client.call_tool("generate_search_queries", user_args)
                
        except Exception as e:
            logger.error(f"Error in LLM tool selection: {e}")
            raise
        finally:
            await self._cleanup_client()

    # 原有接口方法 - 通过LLM调用Server工具实现
    def get_queries(self, topic: str, description: str = "") -> List[str]:
        """生成搜索查询 - 通过LLM选择并调用Server工具"""
        try:
            result = asyncio.run(self._llm_select_and_call_tool(
                f"为主题'{topic}'生成搜索查询",
                topic=topic,
                description=description
            ))
            return result.get('queries', [topic])  # 如果失败，至少返回原始主题
        except Exception as e:
            logger.error(f"Error in get_queries: {e}")
            return [topic]  # 降级处理

    def web_search(self, query: str, topic: str, top_n: int = 10) -> List[str]:
        """单个查询搜索 - 通过LLM选择并调用Server工具"""
        return self.batch_web_search([query], topic, top_n)

    def batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:
        """批量搜索 - 通过LLM选择并调用Server工具"""
        try:
            result = asyncio.run(self._llm_select_and_call_tool(
                f"为主题'{topic}'执行网络搜索",
                queries=queries,
                topic=topic,
                top_n=top_n
            ))
            return result.get('urls', [])
        except Exception as e:
            logger.error(f"Error in batch_web_search: {e}")
            return []

    def snippet_filter(self, topic: str, snippet: str) -> float:
        """计算相似度分数 - 简单实现"""
        try:
            # 简单的相似度计算
            topic_words = set(topic.lower().split())
            snippet_words = set(snippet.lower().split())
            if not topic_words or not snippet_words:
                return 0.0
            intersection = topic_words.intersection(snippet_words)
            return len(intersection) / len(topic_words.union(snippet_words))
        except Exception as e:
            logger.error(f"Error calculating similarity score: {e}")
            return 0.0

    def add_search_engine(self, name: str, config: dict):
        """添加搜索引擎（兼容性方法）"""
        logger.warning("add_search_engine is deprecated in MCP version")
        pass

    def list_available_engines(self) -> List[str]:
        """列出可用搜索引擎（兼容性方法）"""
        return ["google", "bing", "baidu"]

    def crawl_urls(self, topic: str, url_list: List[str], top_n: int = 80,
                   similarity_threshold: float = 80, min_length: int = 350,
                   max_length: int = 20000) -> List[dict]:
        """爬取URL并处理内容 - 通过LLM选择并调用Server工具"""
        try:
            result = asyncio.run(self._llm_select_and_call_tool(
                f"为主题'{topic}'爬取和处理URL内容",
                topic=topic,
                url_list=url_list,
                top_n=top_n,
                similarity_threshold=similarity_threshold,
                min_length=min_length,
                max_length=max_length
            ))
            return result.get('final_results', [])
        except Exception as e:
            logger.error(f"Error in crawl_urls: {e}")
            return []

    def get_conversation_history(self, limit: Optional[int] = None):
        """获取当前模型的对话历史"""
        return self.request_wrapper.get_conversation_history(limit)

    def clear_conversation_history(self):
        """清除当前模型的对话历史"""
        self.request_wrapper.clear_conversation_history()
        logger.info(f"Cleared conversation history for MCP Host model: {self.model}")

    def get_memory_statistics(self):
        """获取内存使用统计"""
        return self.request_wrapper.get_memory_statistics()

    def export_conversation_history(self, output_file: str) -> bool:
        """导出对话历史到文件"""
        return self.request_wrapper.export_conversation_history(output_file)

    def set_memory_enabled(self, enabled: bool):
        """启用或禁用对话历史功能"""
        self.use_memory = enabled
        self.request_wrapper.use_memory = enabled
        logger.info(f"Memory functionality {'enabled' if enabled else 'disabled'} for MCP Host model: {self.model}")


def create_llm_search(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    max_workers: int = 10,
) -> LLM_search:
    return LLM_search(
        model=model,
        infer_type=infer_type,
        max_workers=max_workers
    )
