import os
import json
import logging
import re
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from llm_search_host import LLM_search
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from request.wrapper import RequestWrapper
from request import RequestWrapper
    
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AnalyseInterface:
    def __init__(self,
                 base_dir: str = "new/test",
                 max_interaction_rounds: int = 3,
                 config_path: Optional[str] = None,
                 llm_model: str = "gemini-2.0-flash-thinking-exp-01-21",
                 llm_infer_type: str = "OpenAI"):
 
        self.base_dir = Path(base_dir)
        self.max_interaction_rounds = max_interaction_rounds
        self.config_path = config_path
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.config_dir = self.base_dir / "config"
        self.config_dir.mkdir(exist_ok=True)
        self.llm_model = llm_model
        self.llm_infer_type = llm_infer_type
        self._init_llm_components()
        self._init_llm_search()

        self._load_config()
        self._load_prompt_templates()

        logger.info(f"AnalyseInterface initialized:")
        logger.info(f"  - Base directory: {self.base_dir}")
        logger.info(f"  - Max interaction rounds: {self.max_interaction_rounds}")
        logger.info(f"  - LLM model: {self.llm_model}")
        logger.info(f"  - LLM search engine: {self.llm_search.engine if hasattr(self.llm_search, 'engine') else 'MCP'}")

    def _init_llm_components(self):
        try:
            self.llm_wrapper = RequestWrapper(
                model=self.llm_model,
                infer_type=self.llm_infer_type,
                use_memory=True,
                max_context_messages=10
            )
            logger.info("LLM wrapper initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM wrapper: {e}")
            raise

    def _init_llm_search(self):
        try:
            self.llm_search = LLM_search(
                model=self.llm_model,
                infer_type=self.llm_infer_type,
                max_workers=10
            )
            logger.info("LLM search instance initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM search: {e}")
            raise

    def analyse(self, topic: str, description: Optional[str] = None,
                top_n: int = 20, **kwargs) -> str:
        logger.info(f"Starting comprehensive literature analysis for topic: '{topic}'")

        try:
            logger.info("=== Phase 1: Topic Expansion with LLM ===")
            expanded_topic = self._expand_topic_with_llm(topic, description)

            logger.info(f"=== Phase 2: Interactive Refinement ({self.max_interaction_rounds} rounds) ===")
            refined_topic = self._interactive_refinement(expanded_topic)

            logger.info("=== Phase 3: Literature Retrieval using LLM Search ===")
            literature_results = self._retrieve_literature(refined_topic, top_n)

            logger.info("=== Phase 4: Saving Results to Local Storage ===")
            result_path = self._save_results(literature_results, topic, refined_topic)

            logger.info(f"✅ Analysis completed successfully. Results saved to: {result_path}")
            return str(result_path)

        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            raise
    
    def _load_config(self):
        try:
            if self.config_path and os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {
                    "search": {
                        "default_top_n": 20,
                        "default_engine": "google",
                        "similarity_threshold": 80
                    },
                    "interaction": {
                        "timeout_seconds": 300,
                        "auto_continue": False
                    }
                }
            logger.info("Configuration loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load config, using defaults: {e}")
            self.config = {}

    def _load_prompt_templates(self):
        self.prompts = {
            "topic_expansion": """
你是一个专业的学术研究分析专家。请对以下研究主题进行深度分析和多角度进行扩写。

原始主题：{topic}
主题描述：{description}

请从以下角度进行全面分析：
1. 

请以JSON格式输出，包含以下字段：
{{
    "core_concept": "核心概念定义",
    "research_scope": "研究范围",
    "key_questions": ["问题1", "问题2", "问题3"],
    "sub_fields": ["子领域1", "子领域2", "子领域3"],
    "methodologies": ["方法1", "方法2", "方法3"],
    "current_trends": ["趋势1", "趋势2", "趋势3"],
    "applications": ["应用1", "应用2", "应用3"],
    "search_keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"]
}}
""",
            "refinement": """
基于用户反馈，请优化以下研究分析：

当前分析：
{current_analysis}

用户反馈：
{user_feedback}

请根据反馈调整和改进分析内容，保持JSON格式输出。
""",
            "search_query_generation": """
基于以下精炼的研究分析，生成高质量的学术搜索查询：

研究分析：
{refined_topic}

请生成5-8个不同角度的搜索查询，确保：
1. 覆盖核心概念和子领域
2. 包含最新研究趋势
3. 适合学术搜索引擎
4. 查询具有足够的特异性

以JSON格式返回：
{{
    "queries": ["查询1", "查询2", "查询3", "查询4", "查询5"]
}}
"""
        }

    def _expand_topic_with_llm(self, topic: str, description: Optional[str] = None) -> Dict[str, Any]:
        logger.info(f"Expanding topic with LLM: '{topic}'")

        try:
            prompt = self.prompts["topic_expansion"].format(
                topic=topic,
                description=description or "无额外描述"
            )

            response = self.llm_wrapper.completion(prompt)

            # 解析JSON响应
            expanded_topic = self._parse_json_response(response)

            if not expanded_topic:
                # 如果解析失败，创建基础结构
                expanded_topic = {
                    "core_concept": topic,
                    "research_scope": description or f"{topic}相关研究",
                    "key_questions": [f"{topic}的核心问题"],
                    "sub_fields": [topic],
                    "methodologies": ["文献综述", "实证研究"],
                    "current_trends": [f"{topic}最新发展"],
                    "applications": [f"{topic}的应用"],
                    "search_keywords": [topic]
                }

            logger.info("Topic expansion completed successfully")
            return expanded_topic

        except Exception as e:
            logger.error(f"Topic expansion failed: {e}")
            # 返回基础结构
            return {
                "core_concept": topic,
                "research_scope": description or f"{topic}相关研究",
                "key_questions": [f"{topic}的核心问题"],
                "sub_fields": [topic],
                "methodologies": ["文献综述"],
                "current_trends": [f"{topic}发展趋势"],
                "applications": [f"{topic}应用"],
                "search_keywords": [topic]
            }

    def _parse_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析LLM的JSON响应"""
        try:
            # 尝试直接解析
            if response.strip().startswith('{'):
                return json.loads(response.strip())

            # 查找JSON代码块
            import re
            json_pattern = r'```json\s*(.*?)\s*```'
            match = re.search(json_pattern, response, re.DOTALL)
            if match:
                return json.loads(match.group(1).strip())

            # 查找大括号内容
            brace_pattern = r'\{.*\}'
            match = re.search(brace_pattern, response, re.DOTALL)
            if match:
                return json.loads(match.group(0))

            return None

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {e}")
            return None

    def _create_task_directory(self, task: str) -> Path:
        """创建任务专用目录"""
        # 创建安全的目录名
        safe_task_name = "".join(c for c in task if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_task_name = safe_task_name.replace(' ', '_')

        # 添加时间戳确保唯一性
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        task_dir_name = f"{safe_task_name}_{timestamp}"

        task_dir = self.base_dir / task_dir_name
        task_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        (task_dir / "papers").mkdir(exist_ok=True)
        (task_dir / "analysis").mkdir(exist_ok=True)
        (task_dir / "logs").mkdir(exist_ok=True)

        return task_dir
    
    def _interactive_refinement(self, expanded_topic: Dict[str, Any]) -> Dict[str, Any]:
        """进行x轮人机交互反馈"""
        logger.info(f"Starting interactive refinement (max {self.max_interaction_rounds} rounds)")

        current_analysis = expanded_topic.copy()

        for round_num in range(self.max_interaction_rounds):
            logger.info(f"--- Interaction Round {round_num + 1}/{self.max_interaction_rounds} ---")

            # 展示当前分析结果给用户
            self._display_analysis(current_analysis, round_num + 1)

            # 获取用户反馈
            user_feedback = self._get_user_feedback(round_num + 1)

            # 检查用户是否满意
            if user_feedback.get('satisfied', False):
                logger.info("User satisfied with current analysis, proceeding to literature search")
                break

            # 如果有具体反馈，基于反馈重新分析
            if user_feedback.get('feedback_text'):
                logger.info("Refining analysis based on user feedback")
                current_analysis = self._refine_with_feedback(current_analysis, user_feedback)
            else:
                logger.info("No specific feedback provided, keeping current analysis")

        logger.info("Interactive refinement completed")
        return current_analysis

    def _display_analysis(self, analysis: Dict[str, Any], round_num: int):
        """展示当前分析结果给用户"""
        print(f"\n{'='*60}")
        print(f"第 {round_num} 轮分析结果")
        print(f"{'='*60}")

        print(f"\n🎯 核心概念：{analysis.get('core_concept', 'N/A')}")
        print(f"\n📋 研究范围：{analysis.get('research_scope', 'N/A')}")

        print(f"\n❓ 关键研究问题：")
        for i, question in enumerate(analysis.get('key_questions', []), 1):
            print(f"   {i}. {question}")

        print(f"\n🔬 相关子领域：")
        for i, field in enumerate(analysis.get('sub_fields', []), 1):
            print(f"   {i}. {field}")

        print(f"\n📊 研究方法：")
        for i, method in enumerate(analysis.get('methodologies', []), 1):
            print(f"   {i}. {method}")

        print(f"\n🔥 当前研究热点：")
        for i, trend in enumerate(analysis.get('current_trends', []), 1):
            print(f"   {i}. {trend}")

        print(f"\n💡 潜在应用：")
        for i, app in enumerate(analysis.get('applications', []), 1):
            print(f"   {i}. {app}")

        print(f"\n🔍 搜索关键词：")
        keywords = analysis.get('search_keywords', [])
        print(f"   {', '.join(keywords)}")

    def _get_user_feedback(self, round_num: int) -> Dict[str, Any]:
        """获取用户反馈"""
        print(f"\n{'='*60}")
        print(f"请提供第 {round_num} 轮反馈")
        print(f"{'='*60}")

        try:
            # 询问用户是否满意
            satisfied_input = input("\n您是否满意当前的分析结果？(y/n/回车继续): ").strip().lower()

            if satisfied_input in ['y', 'yes', '是', 'ok']:
                return {'satisfied': True}

            if satisfied_input in ['', 'continue', '继续']:
                if round_num >= self.max_interaction_rounds:
                    return {'satisfied': True}
                else:
                    return {'satisfied': False}

            # 获取具体反馈
            print("\n请提供具体的改进建议（可以包括）：")
            print("- 需要调整的概念定义")
            print("- 需要添加或删除的子领域")
            print("- 需要补充的研究问题")
            print("- 其他任何改进意见")
            print("\n输入您的反馈（回车结束）：")

            feedback_text = input().strip()

            return {
                'satisfied': False,
                'feedback_text': feedback_text,
                'round': round_num
            }

        except KeyboardInterrupt:
            logger.info("User interrupted, proceeding with current analysis")
            return {'satisfied': True}
        except Exception as e:
            logger.warning(f"Error getting user feedback: {e}")
            return {'satisfied': True}

    def _refine_with_feedback(self, current_analysis: Dict[str, Any],
                            user_feedback: Dict[str, Any]) -> Dict[str, Any]:
        """基于用户反馈重新分析"""
        logger.info("Refining analysis based on user feedback")

        try:
            prompt = self.prompts["refinement"].format(
                current_analysis=json.dumps(current_analysis, ensure_ascii=False, indent=2),
                user_feedback=user_feedback.get('feedback_text', '')
            )

            response = self.llm_wrapper.completion(prompt)
            refined_analysis = self._parse_json_response(response)

            if refined_analysis:
                logger.info("Analysis refined successfully")
                return refined_analysis
            else:
                logger.warning("Failed to parse refined analysis, keeping original")
                return current_analysis

        except Exception as e:
            logger.error(f"Error refining analysis: {e}")
            return current_analysis

    def _retrieve_literature(self, refined_topic: Dict[str, Any], top_n: int = 20) -> List[Dict[str, Any]]:
        """调用llm_search模块进行实际文献检索"""
        logger.info("Starting literature retrieval using LLM Search")

        try:
            # 1. 生成搜索查询
            search_queries = self._extract_search_queries(refined_topic)
            logger.info(f"Generated {len(search_queries)} search queries: {search_queries}")

            # 2. 执行批量搜索获取URLs
            core_concept = refined_topic.get('core_concept', 'research')
            urls = self.llm_search.batch_web_search(
                queries=search_queries,
                topic=core_concept,
                top_n=min(top_n * 3, 60)  # 获取更多URL以便后续筛选
            )
            logger.info(f"Retrieved {len(urls)} URLs from web search")

            if not urls:
                logger.warning("No URLs retrieved from web search")
                return []

            # 3. 爬取和分析文献内容
            literature_results = self.llm_search.crawl_urls(
                topic=core_concept,
                url_list=urls,
                top_n=top_n,
                similarity_threshold=self.config.get('search', {}).get('similarity_threshold', 80),
                min_length=350,
                max_length=20000
            )

            logger.info(f"Successfully retrieved {len(literature_results)} literature papers")
            return literature_results

        except Exception as e:
            logger.error(f"Literature retrieval failed: {e}")
            return []

    def _extract_search_queries(self, refined_topic: Dict[str, Any]) -> List[str]:
        """从精炼的主题分析中提取搜索查询"""
        try:
            # 首先尝试使用LLM生成查询
            prompt = self.prompts["search_query_generation"].format(
                refined_topic=json.dumps(refined_topic, ensure_ascii=False, indent=2)
            )

            response = self.llm_wrapper.completion(prompt)
            query_result = self._parse_json_response(response)

            if query_result and 'queries' in query_result:
                queries = query_result['queries']
                logger.info(f"LLM generated {len(queries)} search queries")
                return queries

        except Exception as e:
            logger.warning(f"LLM query generation failed: {e}")

        # 备用方案：基于分析内容构建查询
        queries = []

        # 添加核心概念查询
        core_concept = refined_topic.get('core_concept', '')
        if core_concept:
            queries.append(core_concept)

        # 添加子领域查询
        sub_fields = refined_topic.get('sub_fields', [])
        for field in sub_fields[:3]:  # 限制数量
            if field and field not in queries:
                queries.append(field)

        # 添加研究热点查询
        trends = refined_topic.get('current_trends', [])
        for trend in trends[:2]:
            if trend and trend not in queries:
                queries.append(trend)

        # 添加关键词组合查询
        keywords = refined_topic.get('search_keywords', [])
        if len(keywords) >= 2:
            combined_query = f"{keywords[0]} {keywords[1]}"
            if combined_query not in queries:
                queries.append(combined_query)

        logger.info(f"Fallback method generated {len(queries)} search queries")
        return queries[:8]  # 限制最大查询数量
    
    def _save_results(self, literature_results: List[Dict[str, Any]],
                     original_topic: str, refined_topic: Dict[str, Any]) -> Path:
        """保存检索结果到本地"""
        logger.info("Saving analysis results to local storage")

        try:
            # 创建任务目录
            task_dir = self._create_task_directory(original_topic)

            # 保存文献数据
            papers_dir = task_dir / "papers"
            for i, paper in enumerate(literature_results):
                paper_filename = f"paper_{i+1:03d}.json"
                paper_path = papers_dir / paper_filename

                with open(paper_path, 'w', encoding='utf-8') as f:
                    json.dump(paper, f, ensure_ascii=False, indent=2)

            # 保存分析过程数据
            analysis_dir = task_dir / "analysis"

            # 保存原始主题
            original_topic_path = analysis_dir / "original_topic.json"
            with open(original_topic_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "original_topic": original_topic,
                    "timestamp": datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)

            # 保存精炼后的主题分析
            refined_topic_path = analysis_dir / "refined_topic.json"
            with open(refined_topic_path, 'w', encoding='utf-8') as f:
                json.dump(refined_topic, f, ensure_ascii=False, indent=2)

            # 创建综合分析摘要
            summary = self._create_analysis_summary(literature_results, original_topic, refined_topic)
            summary_path = task_dir / "analysis_summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            # 创建README文件
            readme_path = task_dir / "README.md"
            self._create_readme_file(readme_path, original_topic, refined_topic, len(literature_results))

            logger.info(f"Successfully saved {len(literature_results)} papers and analysis to {task_dir}")
            return task_dir

        except Exception as e:
            logger.error(f"Error saving results: {e}")
            raise

    def _create_analysis_summary(self, literature_results: List[Dict[str, Any]],
                               original_topic: str, refined_topic: Dict[str, Any]) -> Dict[str, Any]:
        """创建分析摘要"""
        return {
            "analysis_info": {
                "original_topic": original_topic,
                "core_concept": refined_topic.get('core_concept', ''),
                "research_scope": refined_topic.get('research_scope', ''),
                "analysis_timestamp": datetime.now().isoformat(),
                "interaction_rounds": self.max_interaction_rounds
            },
            "literature_summary": {
                "total_papers": len(literature_results),
                "paper_files": [f"papers/paper_{i+1:03d}.json" for i in range(len(literature_results))],
                "average_similarity": sum(paper.get('similarity_score', 0) for paper in literature_results) / len(literature_results) if literature_results else 0,
                "url_sources": [paper.get('url', '') for paper in literature_results]
            },
            "research_analysis": {
                "key_questions": refined_topic.get('key_questions', []),
                "sub_fields": refined_topic.get('sub_fields', []),
                "methodologies": refined_topic.get('methodologies', []),
                "current_trends": refined_topic.get('current_trends', []),
                "applications": refined_topic.get('applications', []),
                "search_keywords": refined_topic.get('search_keywords', [])
            },
            "system_info": {
                "llm_model": self.llm_model,
                "search_engine": getattr(self.llm_search, 'engine', 'MCP'),
                "base_directory": str(self.base_dir),
                "config": self.config
            }
        }

    def _create_readme_file(self, readme_path: Path, original_topic: str,
                          refined_topic: Dict[str, Any], paper_count: int):
        """创建README文件"""
        readme_content = f"""# 文献分析报告

## 基本信息
- **原始主题**: {original_topic}
- **核心概念**: {refined_topic.get('core_concept', 'N/A')}
- **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **检索文献数量**: {paper_count}

## 研究范围
{refined_topic.get('research_scope', 'N/A')}

## 关键研究问题
"""
        for i, question in enumerate(refined_topic.get('key_questions', []), 1):
            readme_content += f"{i}. {question}\n"

        readme_content += f"""
## 相关子领域
"""
        for i, field in enumerate(refined_topic.get('sub_fields', []), 1):
            readme_content += f"{i}. {field}\n"

        readme_content += f"""
## 研究方法论
"""
        for i, method in enumerate(refined_topic.get('methodologies', []), 1):
            readme_content += f"{i}. {method}\n"

        readme_content += f"""
## 当前研究热点
"""
        for i, trend in enumerate(refined_topic.get('current_trends', []), 1):
            readme_content += f"{i}. {trend}\n"

        readme_content += f"""
## 潜在应用价值
"""
        for i, app in enumerate(refined_topic.get('applications', []), 1):
            readme_content += f"{i}. {app}\n"

        readme_content += f"""
## 搜索关键词
{', '.join(refined_topic.get('search_keywords', []))}

## 文件结构
- `papers/`: 检索到的文献文件
- `analysis/`: 分析过程数据
- `analysis_summary.json`: 综合分析摘要
- `README.md`: 本文件

## 使用说明
1. 查看 `analysis_summary.json` 获取完整的分析摘要
2. 浏览 `papers/` 目录中的文献文件
3. 参考 `analysis/refined_topic.json` 了解详细的主题分析结果
"""

        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)

    # 兼容性方法（保留原有接口）
    def _save_papers_to_directory(self, papers: List[Dict[str, Any]],
                                 task_dir: Path) -> None:
        """兼容性方法：保存论文到目录"""
        logger.warning("Using deprecated _save_papers_to_directory method")

        try:
            papers_dir = task_dir / "papers"
            papers_dir.mkdir(exist_ok=True)

            for i, paper in enumerate(papers):
                paper_filename = f"paper_{i+1:03d}.json"
                paper_path = papers_dir / paper_filename

                with open(paper_path, 'w', encoding='utf-8') as f:
                    json.dump(paper, f, ensure_ascii=False, indent=2)

            summary_data = {
                "task": task_dir.name,
                "total_papers": len(papers),
                "paper_files": [f"papers/paper_{i+1:03d}.json" for i in range(len(papers))],
                "created_at": datetime.now().isoformat()
            }

            summary_path = task_dir / "summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(papers)} papers to {task_dir}")

        except Exception as e:
            logger.error(f"Error saving papers to directory: {e}")
            raise


def analyse(task: str, description: Optional[str] = None,
           top_n: int = 20, max_interaction_rounds: int = 3, **kwargs) -> str:
    """
    便捷函数：执行完整的文献分析工作流

    Args:
        task: 研究主题
        description: 主题描述
        top_n: 返回的文献数量
        max_interaction_rounds: 最大人机交互轮数
        **kwargs: 其他参数

    Returns:
        str: 结果保存路径
    """
    analyser = AnalyseInterface(
        max_interaction_rounds=max_interaction_rounds,
        **kwargs
    )
    return analyser.analyse(task, description, top_n)


def create_analyser(base_dir: str = "new/test",
                   max_interaction_rounds: int = 3,
                   llm_model: str = "gemini-2.0-flash-thinking-exp-01-21") -> AnalyseInterface:
    """
    创建分析器实例的工厂函数

    Args:
        base_dir: 基础保存目录
        max_interaction_rounds: 最大人机交互轮数
        llm_model: LLM模型名称

    Returns:
        AnalyseInterface: 分析器实例
    """
    return AnalyseInterface(
        base_dir=base_dir,
        max_interaction_rounds=max_interaction_rounds,
        llm_model=llm_model
    )


if __name__ == "__main__":
    # 测试代码
    import sys

    try:
        print("🚀 启动文献分析系统测试")
        print("="*60)

        # 测试基本功能
        test_task = "machine learning optimization"
        test_description = "Research on optimization techniques in machine learning algorithms"

        print(f"📋 测试主题: {test_task}")
        print(f"📝 主题描述: {test_description}")
        print(f"📚 目标文献数量: 5")
        print(f"🔄 交互轮数: 2")

        # 执行分析
        result_dir = analyse(
            task=test_task,
            description=test_description,
            top_n=5,
            max_interaction_rounds=2
        )

        print(f"\n✅ 分析完成！结果保存到: {result_dir}")
        print(f"📁 请查看目录中的以下文件：")
        print(f"   - README.md: 分析报告")
        print(f"   - analysis_summary.json: 综合摘要")
        print(f"   - papers/: 检索到的文献")
        print(f"   - analysis/: 分析过程数据")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
